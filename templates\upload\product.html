{% extends "base.html" %}

{% block title %}Upload Product - CraftConnect{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Upload New Product</h4>
                </div>
                <div class="card-body">
                    <form id="productForm" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Product Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category *</label>
                                    <select class="form-select" id="category" name="category" required>
                                        <option value="">Select Category</option>
                                        <option value="pottery">Pottery</option>
                                        <option value="textiles">Textiles</option>
                                        <option value="woodwork">Woodwork</option>
                                        <option value="metalwork">Metalwork</option>
                                        <option value="jewelry">Jewelry</option>
                                        <option value="paintings">Paintings</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                placeholder="Describe your product, materials used, crafting techniques, etc."></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Price ($) *</label>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           min="0" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quantity" class="form-label">Quantity Available</label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" 
                                           min="1" value="1">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="image" class="form-label">Product Image</label>
                            <input type="file" class="form-control" id="image" name="image" 
                                   accept="image/*" onchange="previewImage(this)">
                            <div class="form-text">Upload a high-quality image of your product (PNG, JPG, JPEG, GIF)</div>
                        </div>
                        
                        <!-- Image Preview -->
                        <div class="mb-3" id="imagePreview" style="display: none;">
                            <label class="form-label">Image Preview</label>
                            <div class="border rounded p-2">
                                <img id="previewImg" src="" alt="Preview" class="img-fluid" style="max-height: 200px;">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="aiAnalysis" name="aiAnalysis" checked>
                                <label class="form-check-label" for="aiAnalysis">
                                    Enable AI Quality Analysis
                                </label>
                                <div class="form-text">Allow our AI to analyze your product for quality assessment and categorization</div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-secondary me-md-2" onclick="resetForm()">Reset</button>
                            <button type="submit" class="btn btn-primary">Upload Product</button>
                        </div>
                    </form>
                    
                    <div id="message" class="mt-3"></div>
                    
                    <!-- Upload Progress -->
                    <div id="uploadProgress" class="mt-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">Uploading and processing...</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
    }
}

function resetForm() {
    document.getElementById('productForm').reset();
    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('message').innerHTML = '';
}

document.getElementById('productForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const messageDiv = document.getElementById('message');
    const progressDiv = document.getElementById('uploadProgress');
    const submitBtn = this.querySelector('button[type="submit"]');
    
    // Show progress and disable submit button
    progressDiv.style.display = 'block';
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Uploading...';
    
    try {
        const response = await fetch('/upload/product', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            messageDiv.innerHTML = '<div class="alert alert-success">' + result.message + '</div>';
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 2000);
        } else {
            messageDiv.innerHTML = '<div class="alert alert-danger">' + result.message + '</div>';
        }
    } catch (error) {
        messageDiv.innerHTML = '<div class="alert alert-danger">An error occurred. Please try again.</div>';
    } finally {
        // Hide progress and re-enable submit button
        progressDiv.style.display = 'none';
        submitBtn.disabled = false;
        submitBtn.innerHTML = 'Upload Product';
    }
});
</script>
{% endblock %}
